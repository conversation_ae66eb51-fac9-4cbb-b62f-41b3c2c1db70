package service

import (
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PluginDistribution 插件分发信息
type PluginDistribution struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	DeviceTypes  []string          `json:"device_types"`
	BinaryPath   string            `json:"binary_path"`
	Checksum     string            `json:"checksum"`
	Size         int64             `json:"size"`
	Dependencies []string          `json:"dependencies"`
	Config       map[string]string `json:"config"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// AgentPluginRequirement Agent插件需求
type AgentPluginRequirement struct {
	AgentID     string    `json:"agent_id"`
	DeviceTypes []string  `json:"device_types"`
	RequestedAt time.Time `json:"requested_at"`
}

// PluginDistributionStatus 插件分发状态
type PluginDistributionStatus struct {
	AgentID      string     `json:"agent_id"`
	PluginID     string     `json:"plugin_id"`
	Status       string     `json:"status"` // requested, downloading, downloaded, installed, failed
	Progress     float64    `json:"progress"`
	ErrorMessage string     `json:"error_message,omitempty"`
	StartedAt    time.Time  `json:"started_at"`
	CompletedAt  *time.Time `json:"completed_at,omitempty"`
}

// PluginRegistryService 插件注册表服务
type PluginRegistryService struct {
	mu                  sync.RWMutex
	plugins             map[string]*PluginDistribution       // pluginID -> distribution
	agentRequirements   map[string]*AgentPluginRequirement   // agentID -> requirement
	distributionStatus  map[string]*PluginDistributionStatus // "agentID:pluginID" -> status
	pluginsByDeviceType map[string][]*PluginDistribution     // deviceType -> plugins
	pluginDir           string
	logger              *zap.Logger

	// 配置
	maxPluginSize          int64
	supportedDeviceTypes   []string
	enableAutoDistribution bool
	cleanupInterval        time.Duration
}

// NewPluginRegistryService 创建新的插件注册表服务
func NewPluginRegistryService(pluginDir string, logger *zap.Logger) *PluginRegistryService {
	service := &PluginRegistryService{
		plugins:                make(map[string]*PluginDistribution),
		agentRequirements:      make(map[string]*AgentPluginRequirement),
		distributionStatus:     make(map[string]*PluginDistributionStatus),
		pluginsByDeviceType:    make(map[string][]*PluginDistribution),
		pluginDir:              pluginDir,
		logger:                 logger,
		maxPluginSize:          100 * 1024 * 1024, // 100MB
		supportedDeviceTypes:   []string{"mysql", "redis", "system", "postgresql", "mongodb"},
		enableAutoDistribution: true,
		cleanupInterval:        24 * time.Hour,
	}

	// 自动发现和注册现有插件
	if err := service.discoverAndRegisterExistingPlugins(); err != nil {
		logger.Error("自动发现插件失败", zap.Error(err))
	}

	// 启动后台服务
	go service.startBackgroundServices()

	return service
}

// RegisterPlugin 注册插件到注册表
func (s *PluginRegistryService) RegisterPlugin(ctx context.Context, pluginPath string, metadata *PluginDistribution) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 验证插件文件
	if err := s.validatePluginFile(pluginPath); err != nil {
		return fmt.Errorf("插件文件验证失败: %w", err)
	}

	// 计算文件校验和
	checksum, size, err := s.calculateFileInfo(pluginPath)
	if err != nil {
		return fmt.Errorf("计算文件信息失败: %w", err)
	}

	// 复制插件到插件目录
	pluginFileName := fmt.Sprintf("%s-%s.so", metadata.Name, metadata.Version)
	destPath := filepath.Join(s.pluginDir, pluginFileName)

	if err := s.copyFile(pluginPath, destPath); err != nil {
		return fmt.Errorf("复制插件文件失败: %w", err)
	}

	// 更新元数据
	metadata.BinaryPath = destPath
	metadata.Checksum = checksum
	metadata.Size = size
	metadata.UpdatedAt = time.Now()
	if metadata.CreatedAt.IsZero() {
		metadata.CreatedAt = time.Now()
	}

	// 存储到注册表
	s.plugins[metadata.ID] = metadata

	// 更新设备类型索引
	for _, deviceType := range metadata.DeviceTypes {
		s.pluginsByDeviceType[deviceType] = append(s.pluginsByDeviceType[deviceType], metadata)
	}

	s.logger.Info("插件注册成功",
		zap.String("plugin_id", metadata.ID),
		zap.String("name", metadata.Name),
		zap.String("version", metadata.Version),
		zap.Strings("device_types", metadata.DeviceTypes))

	// 检查是否有Agent需要此插件
	if s.enableAutoDistribution {
		go s.checkAndDistributePlugin(metadata)
	}

	return nil
}

// RequestPlugin Agent请求插件
func (s *PluginRegistryService) RequestPlugin(ctx context.Context, agentID string, deviceTypes []string) ([]*PluginDistribution, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 记录Agent需求
	s.agentRequirements[agentID] = &AgentPluginRequirement{
		AgentID:     agentID,
		DeviceTypes: deviceTypes,
		RequestedAt: time.Now(),
	}

	var requiredPlugins []*PluginDistribution

	// 为每个设备类型查找合适的插件
	for _, deviceType := range deviceTypes {
		plugins := s.pluginsByDeviceType[deviceType]
		if len(plugins) > 0 {
			// 选择最新版本的插件
			latestPlugin := s.getLatestPlugin(plugins)
			if latestPlugin != nil {
				requiredPlugins = append(requiredPlugins, latestPlugin)

				// 记录分发状态
				statusKey := fmt.Sprintf("%s:%s", agentID, latestPlugin.ID)
				s.distributionStatus[statusKey] = &PluginDistributionStatus{
					AgentID:   agentID,
					PluginID:  latestPlugin.ID,
					Status:    "requested",
					Progress:  0.0,
					StartedAt: time.Now(),
				}
			}
		}
	}

	s.logger.Info("Agent请求插件",
		zap.String("agent_id", agentID),
		zap.Strings("device_types", deviceTypes),
		zap.Int("plugins_found", len(requiredPlugins)))

	return requiredPlugins, nil
}

// GetPluginBinary 获取插件二进制文件
func (s *PluginRegistryService) GetPluginBinary(ctx context.Context, agentID, pluginID string) (io.ReadCloser, error) {
	s.mu.RLock()
	plugin, exists := s.plugins[pluginID]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("插件不存在: %s", pluginID)
	}

	// 更新分发状态
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)
	s.updateDistributionStatus(statusKey, "downloading", 0.0, "")

	// 打开文件
	file, err := os.Open(plugin.BinaryPath)
	if err != nil {
		s.updateDistributionStatus(statusKey, "failed", 0.0, err.Error())
		return nil, fmt.Errorf("打开插件文件失败: %w", err)
	}

	s.logger.Info("开始分发插件",
		zap.String("agent_id", agentID),
		zap.String("plugin_id", pluginID),
		zap.String("plugin_path", plugin.BinaryPath))

	return file, nil
}

// UpdateDistributionStatus 更新插件分发状态
func (s *PluginRegistryService) UpdateDistributionStatus(agentID, pluginID, status string, progress float64, errorMsg string) {
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)
	s.updateDistributionStatus(statusKey, status, progress, errorMsg)
}

// GetPluginsByDeviceType 根据设备类型获取插件列表
func (s *PluginRegistryService) GetPluginsByDeviceType(deviceType string) []*PluginDistribution {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.pluginsByDeviceType[deviceType]
}

// ListAllPlugins 列出所有注册的插件
func (s *PluginRegistryService) ListAllPlugins() []*PluginDistribution {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var plugins []*PluginDistribution
	for _, plugin := range s.plugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// GetDistributionStatus 获取分发状态
func (s *PluginRegistryService) GetDistributionStatus(agentID string) []*PluginDistributionStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var statuses []*PluginDistributionStatus
	for _, status := range s.distributionStatus {
		if status.AgentID == agentID {
			statuses = append(statuses, status)
		}
	}

	return statuses
}

// RemovePlugin 移除插件
func (s *PluginRegistryService) RemovePlugin(ctx context.Context, pluginID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	plugin, exists := s.plugins[pluginID]
	if !exists {
		return fmt.Errorf("插件不存在: %s", pluginID)
	}

	// 删除文件
	if err := os.Remove(plugin.BinaryPath); err != nil {
		s.logger.Warn("删除插件文件失败", zap.String("path", plugin.BinaryPath), zap.Error(err))
	}

	// 从注册表中移除
	delete(s.plugins, pluginID)

	// 从设备类型索引中移除
	for _, deviceType := range plugin.DeviceTypes {
		plugins := s.pluginsByDeviceType[deviceType]
		for i, p := range plugins {
			if p.ID == pluginID {
				s.pluginsByDeviceType[deviceType] = append(plugins[:i], plugins[i+1:]...)
				break
			}
		}
	}

	s.logger.Info("插件移除成功", zap.String("plugin_id", pluginID))
	return nil
}

// Extended PluginDistribution for the distribution service
type ExtendedPluginDistribution struct {
	*PluginDistribution
	Description            string            `json:"description"`
	SupportedDeviceTypes   []string          `json:"supported_device_types"`
	SupportedArchitectures []string          `json:"supported_architectures"`
	SupportedOS            []string          `json:"supported_os"`
	Configuration          map[string]string `json:"configuration"`
	Author                 string            `json:"author"`
}

// GetPluginForAgent 根据Agent需求获取合适的插件
func (s *PluginRegistryService) GetPluginForAgent(pluginName, version, deviceType, architecture, os string) (*ExtendedPluginDistribution, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 如果指定了版本，直接查找
	if version != "" {
		pluginID := fmt.Sprintf("%s-%s", pluginName, version)
		if plugin, exists := s.plugins[pluginID]; exists {
			if s.isPluginCompatible(plugin, deviceType, architecture, os) {
				return s.toExtendedDistribution(plugin), nil
			}
		}
		return nil, fmt.Errorf("指定版本的插件不存在或不兼容: %s-%s", pluginName, version)
	}

	// 查找最新的兼容版本
	var latestPlugin *PluginDistribution
	for _, plugin := range s.plugins {
		if plugin.Name == pluginName && s.isPluginCompatible(plugin, deviceType, architecture, os) {
			if latestPlugin == nil || plugin.CreatedAt.After(latestPlugin.CreatedAt) {
				latestPlugin = plugin
			}
		}
	}

	if latestPlugin == nil {
		return nil, fmt.Errorf("未找到兼容的插件: %s (device_type: %s, arch: %s, os: %s)",
			pluginName, deviceType, architecture, os)
	}

	return s.toExtendedDistribution(latestPlugin), nil
}

// RecordDistribution 记录插件分发事件
func (s *PluginRegistryService) RecordDistribution(agentID, pluginName, version, status, errorMessage string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	pluginID := fmt.Sprintf("%s-%s", pluginName, version)
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)

	if distribution, exists := s.distributionStatus[statusKey]; exists {
		distribution.Status = status
		distribution.ErrorMessage = errorMessage
		if status == "success" || status == "failed" {
			now := time.Now()
			distribution.CompletedAt = &now
			distribution.Progress = 100.0
		}
	} else {
		now := time.Now()
		s.distributionStatus[statusKey] = &PluginDistributionStatus{
			AgentID:      agentID,
			PluginID:     pluginID,
			Status:       status,
			Progress:     100.0,
			ErrorMessage: errorMessage,
			StartedAt:    now,
			CompletedAt:  &now,
		}
	}

	s.logger.Info("记录插件分发事件",
		zap.String("agent_id", agentID),
		zap.String("plugin_name", pluginName),
		zap.String("version", version),
		zap.String("status", status))

	return nil
}

// UpdateAgentPluginStatus 更新Agent插件状态
func (s *PluginRegistryService) UpdateAgentPluginStatus(agentID, pluginName, version, status, errorMessage string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	pluginID := fmt.Sprintf("%s-%s", pluginName, version)
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)

	if distribution, exists := s.distributionStatus[statusKey]; exists {
		distribution.Status = status
		distribution.ErrorMessage = errorMessage
		if status == "loaded" || status == "running" {
			distribution.Progress = 100.0
		}
	} else {
		// 如果状态不存在，创建新的状态记录
		s.distributionStatus[statusKey] = &PluginDistributionStatus{
			AgentID:      agentID,
			PluginID:     pluginID,
			Status:       status,
			Progress:     100.0,
			ErrorMessage: errorMessage,
			StartedAt:    time.Now(),
		}
	}

	s.logger.Debug("更新Agent插件状态",
		zap.String("agent_id", agentID),
		zap.String("plugin_name", pluginName),
		zap.String("version", version),
		zap.String("status", status))

	return nil
}

// isPluginCompatible 检查插件是否与Agent环境兼容
func (s *PluginRegistryService) isPluginCompatible(plugin *PluginDistribution, deviceType, architecture, os string) bool {
	// 检查设备类型兼容性
	if deviceType != "" {
		found := false
		for _, supportedType := range plugin.DeviceTypes {
			if supportedType == deviceType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 检查架构兼容性
	if architecture != "" {
		// 从插件配置中获取支持的架构信息，如果没有配置则默认支持所有主流架构
		supportedArchs := []string{"amd64", "arm64", "x86_64"}
		if archConfig, exists := plugin.Config["supported_architectures"]; exists {
			// 如果插件配置中指定了架构支持，使用配置的值
			// 格式: "amd64,arm64"
			supportedArchs = parseCommaSeparatedString(archConfig)
		}

		found := false
		for _, supportedArch := range supportedArchs {
			if supportedArch == architecture {
				found = true
				break
			}
		}
		if !found {
			s.logger.Debug("插件架构不兼容",
				zap.String("plugin", plugin.Name),
				zap.String("required_arch", architecture),
				zap.Strings("supported_archs", supportedArchs))
			return false
		}
	}

	// 检查操作系统兼容性
	if os != "" {
		// 从插件配置中获取支持的操作系统信息，如果没有配置则默认支持所有主流操作系统
		supportedOS := []string{"linux", "windows", "darwin"}
		if osConfig, exists := plugin.Config["supported_os"]; exists {
			// 如果插件配置中指定了操作系统支持，使用配置的值
			// 格式: "linux,darwin,windows"
			supportedOS = parseCommaSeparatedString(osConfig)
		}

		found := false
		for _, supportedOSItem := range supportedOS {
			if supportedOSItem == os {
				found = true
				break
			}
		}
		if !found {
			s.logger.Debug("插件操作系统不兼容",
				zap.String("plugin", plugin.Name),
				zap.String("required_os", os),
				zap.Strings("supported_os", supportedOS))
			return false
		}
	}

	return true
}

// toExtendedDistribution 转换为扩展的插件分发信息
func (s *PluginRegistryService) toExtendedDistribution(plugin *PluginDistribution) *ExtendedPluginDistribution {
	return &ExtendedPluginDistribution{
		PluginDistribution:     plugin,
		Description:            fmt.Sprintf("Plugin for %s", plugin.Name),
		SupportedDeviceTypes:   plugin.DeviceTypes,
		SupportedArchitectures: []string{"amd64", "arm64"},             // 默认支持的架构
		SupportedOS:            []string{"linux", "windows", "darwin"}, // 默认支持的操作系统
		Configuration:          plugin.Config,
		Author:                 "System",
	}
}

// 私有方法

func (s *PluginRegistryService) validatePluginFile(pluginPath string) error {
	stat, err := os.Stat(pluginPath)
	if err != nil {
		return fmt.Errorf("文件不存在: %w", err)
	}

	if stat.Size() > s.maxPluginSize {
		return fmt.Errorf("插件文件过大: %d bytes (最大: %d)", stat.Size(), s.maxPluginSize)
	}

	// TODO: 验证插件签名
	// TODO: 验证插件格式

	return nil
}

func (s *PluginRegistryService) calculateFileInfo(filePath string) (string, int64, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", 0, err
	}
	defer file.Close()

	hash := sha256.New()
	size, err := io.Copy(hash, file)
	if err != nil {
		return "", 0, err
	}

	checksum := fmt.Sprintf("%x", hash.Sum(nil))
	return checksum, size, nil
}

func (s *PluginRegistryService) copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

func (s *PluginRegistryService) getLatestPlugin(plugins []*PluginDistribution) *PluginDistribution {
	if len(plugins) == 0 {
		return nil
	}

	latest := plugins[0]
	for _, plugin := range plugins[1:] {
		if plugin.UpdatedAt.After(latest.UpdatedAt) {
			latest = plugin
		}
	}

	return latest
}

func (s *PluginRegistryService) updateDistributionStatus(statusKey, status string, progress float64, errorMsg string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if existingStatus, exists := s.distributionStatus[statusKey]; exists {
		existingStatus.Status = status
		existingStatus.Progress = progress
		existingStatus.ErrorMessage = errorMsg

		if status == "installed" || status == "failed" {
			now := time.Now()
			existingStatus.CompletedAt = &now
		}
	}
}

func (s *PluginRegistryService) checkAndDistributePlugin(plugin *PluginDistribution) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 检查哪些Agent需要这个插件
	for agentID, requirement := range s.agentRequirements {
		for _, deviceType := range requirement.DeviceTypes {
			for _, pluginDeviceType := range plugin.DeviceTypes {
				if deviceType == pluginDeviceType {
					// Agent需要这个插件，发送通知
					s.logger.Info("检测到Agent需要新插件",
						zap.String("agent_id", agentID),
						zap.String("plugin_id", plugin.ID),
						zap.String("device_type", deviceType))

					// TODO: 发送插件分发通知给Agent
					break
				}
			}
		}
	}
}

func (s *PluginRegistryService) startBackgroundServices() {
	// 定期清理过期的分发状态
	cleanupTicker := time.NewTicker(s.cleanupInterval)
	defer cleanupTicker.Stop()

	for range cleanupTicker.C {
		s.cleanupExpiredStatuses()
	}
}

func (s *PluginRegistryService) cleanupExpiredStatuses() {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	expireTime := 7 * 24 * time.Hour // 7天

	for key, status := range s.distributionStatus {
		if status.CompletedAt != nil && now.Sub(*status.CompletedAt) > expireTime {
			delete(s.distributionStatus, key)
		} else if status.CompletedAt == nil && now.Sub(status.StartedAt) > expireTime {
			// 未完成的状态超过7天也清理
			delete(s.distributionStatus, key)
		}
	}

	s.logger.Debug("清理过期的插件分发状态")
}

// parseCommaSeparatedString 解析逗号分隔的字符串
func parseCommaSeparatedString(value string) []string {
	if value == "" {
		return []string{}
	}

	parts := strings.Split(value, ",")
	var result []string
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// discoverAndRegisterExistingPlugins 自动发现和注册现有插件
func (s *PluginRegistryService) discoverAndRegisterExistingPlugins() error {
	s.logger.Info("开始自动发现插件", zap.String("plugin_dir", s.pluginDir))

	// 确保插件目录存在
	if err := os.MkdirAll(s.pluginDir, 0755); err != nil {
		return fmt.Errorf("创建插件目录失败: %w", err)
	}

	// 扫描多个可能的插件目录
	pluginDirs := []string{
		s.pluginDir,
		"./plugins/build",
		"./plugins/examples/system-collector",
		"./plugins/examples/mysql-collector",
	}

	registeredCount := 0

	for _, dir := range pluginDirs {
		count, err := s.scanPluginDirectory(dir)
		if err != nil {
			s.logger.Warn("扫描插件目录失败", zap.String("dir", dir), zap.Error(err))
			continue
		}
		registeredCount += count
	}

	s.logger.Info("插件自动发现完成",
		zap.Int("total_registered", registeredCount),
		zap.Int("total_plugins", len(s.plugins)))

	return nil
}

// scanPluginDirectory 扫描指定目录中的插件
func (s *PluginRegistryService) scanPluginDirectory(dir string) (int, error) {
	// 检查目录是否存在
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return 0, nil // 目录不存在，跳过
	}

	s.logger.Debug("扫描插件目录", zap.String("dir", dir))

	registeredCount := 0

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理 .so 文件 (Linux 动态库) 或可执行文件
		if filepath.Ext(path) == ".so" || (info.Mode()&0111 != 0 && !info.IsDir()) {
			if err := s.registerPluginFromFile(path); err != nil {
				s.logger.Warn("注册插件失败", zap.String("path", path), zap.Error(err))
			} else {
				registeredCount++
			}
		}

		return nil
	})

	return registeredCount, err
}

// registerPluginFromFile 从文件注册插件
func (s *PluginRegistryService) registerPluginFromFile(pluginPath string) error {
	// 从文件名推断插件信息
	fileName := filepath.Base(pluginPath)
	nameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))

	// 解析插件名称和版本（如果包含版本信息）
	parts := strings.Split(nameWithoutExt, "-")
	if len(parts) < 1 {
		return fmt.Errorf("无法解析插件名称: %s", fileName)
	}

	pluginName := parts[0]
	version := "1.0.0" // 默认版本

	// 如果文件名包含版本信息，尝试解析
	if len(parts) > 1 {
		// 检查最后一部分是否像版本号
		lastPart := parts[len(parts)-1]
		if strings.Contains(lastPart, ".") || len(lastPart) <= 5 {
			version = lastPart
			pluginName = strings.Join(parts[:len(parts)-1], "-")
		} else {
			pluginName = nameWithoutExt
		}
	}

	// 根据插件名称推断设备类型
	deviceTypes := s.inferDeviceTypes(pluginName)
	if len(deviceTypes) == 0 {
		s.logger.Warn("无法推断插件设备类型", zap.String("plugin_name", pluginName))
		return nil // 跳过未知类型的插件
	}

	// 计算文件信息
	checksum, size, err := s.calculateFileInfo(pluginPath)
	if err != nil {
		return fmt.Errorf("计算文件信息失败: %w", err)
	}

	// 创建插件分发信息
	pluginID := fmt.Sprintf("%s-%s", pluginName, version)

	// 检查是否已经注册
	if _, exists := s.plugins[pluginID]; exists {
		s.logger.Debug("插件已存在，跳过注册", zap.String("plugin_id", pluginID))
		return nil
	}

	plugin := &PluginDistribution{
		ID:          pluginID,
		Name:        pluginName,
		Version:     version,
		DeviceTypes: deviceTypes,
		BinaryPath:  pluginPath,
		Checksum:    checksum,
		Size:        size,
		Config:      make(map[string]string),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置默认配置
	plugin.Config["supported_architectures"] = "amd64,arm64,x86_64"
	plugin.Config["supported_os"] = "linux,windows,darwin"

	// 注册插件
	s.plugins[pluginID] = plugin

	// 更新设备类型索引
	for _, deviceType := range deviceTypes {
		s.pluginsByDeviceType[deviceType] = append(s.pluginsByDeviceType[deviceType], plugin)
	}

	s.logger.Info("自动注册插件成功",
		zap.String("plugin_id", pluginID),
		zap.String("name", pluginName),
		zap.String("version", version),
		zap.Strings("device_types", deviceTypes),
		zap.String("path", pluginPath))

	return nil
}

// inferDeviceTypes 根据插件名称推断设备类型
func (s *PluginRegistryService) inferDeviceTypes(pluginName string) []string {
	name := strings.ToLower(pluginName)

	// 基于插件名称的设备类型映射
	deviceTypeMap := map[string][]string{
		"system":     {"system"},
		"mysql":      {"mysql"},
		"redis":      {"redis"},
		"postgresql": {"postgresql"},
		"postgres":   {"postgresql"},
		"mongodb":    {"mongodb"},
		"mongo":      {"mongodb"},
		"nginx":      {"nginx"},
		"apache":     {"apache"},
		"docker":     {"docker"},
		"kubernetes": {"kubernetes"},
		"k8s":        {"kubernetes"},
	}

	// 通用插件类型（不绑定特定设备）
	generalPluginTypes := map[string][]string{
		"alerter":  {"general"}, // 告警器插件
		"analyzer": {"general"}, // 分析器插件
		"email":    {"general"}, // 邮件插件
		"webhook":  {"general"}, // Webhook插件
		"sms":      {"general"}, // 短信插件
		"enhanced": {"general"}, // 增强型插件
		"simple":   {"general"}, // 简单插件
	}

	// 查找完全匹配
	if types, ok := deviceTypeMap[name]; ok {
		return types
	}

	// 查找通用插件类型
	for keyword, types := range generalPluginTypes {
		if strings.Contains(name, keyword) {
			return types
		}
	}

	// 查找部分匹配
	for keyword, types := range deviceTypeMap {
		if strings.Contains(name, keyword) {
			return types
		}
	}

	// 如果包含 "collector"，尝试从前缀推断
	if strings.Contains(name, "collector") {
		for keyword, types := range deviceTypeMap {
			if strings.HasPrefix(name, keyword) {
				return types
			}
		}
		// 如果无法从前缀推断，但确实是collector，分配general类型
		return []string{"general"}
	}

	s.logger.Debug("未能推断插件设备类型",
		zap.String("plugin_name", pluginName),
		zap.String("normalized_name", name))

	return []string{} // 无法推断
}
