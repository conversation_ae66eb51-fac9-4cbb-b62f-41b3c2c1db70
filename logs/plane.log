2025-05-24 23:03:41.710699000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-24 23:03:41.711151000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-24 23:03:41.896143000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-24 23:03:41.896217000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-24 23:03:41.896231000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-24 23:03:41.898868000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-24 23:03:41.898883000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-24 23:03:41.899984000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-24 23:03:41.900977000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-24 23:03:41.901233000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-24 23:03:41.901255000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-24 23:03:41.901263000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-24 23:03:41.901272000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-24 23:03:41.901279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-24 23:03:46.112212000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "test-agent-001", "IP": "*************"}
2025-05-24 23:03:46.112256000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "test-agent-001", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-24 23:03:46.113380000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:85	更新 Agent 信息	{"agentID": "test-agent-001"}
2025-05-24 23:03:46.113902000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "test-agent-001"}
2025-05-24 23:03:46.114000000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "test-agent-001"}
2025-05-24 23:03:46.115337000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "test-agent-001", "taskID": "test-agent-001", "状态": "connected"}
2025-05-24 23:04:01.141279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "test-agent-001"}
2025-05-24 23:04:31.243667000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:109	正在关闭服务...
2025-05-24 23:04:31.243735000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:93	正在停止 HTTP API 服务器
2025-05-24 23:04:31.243859000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:102	HTTP API 服务器已停止
2025-05-24 23:04:31.244002000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "test-agent-001"}
2025-05-24 23:04:31.243881000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-24 23:04:31.243985000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-24 23:04:31.244002000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-24 23:04:31.244048000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "test-agent-001", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-24 23:04:31.250091000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已关闭
2025-05-24 23:21:41.346775000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-24 23:21:41.357514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-24 23:21:41.524093000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-24 23:21:41.524188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-24 23:21:41.524201000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-24 23:21:41.529193000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-24 23:21:41.529216000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-24 23:21:41.530700000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-24 23:21:41.531127000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-24 23:21:41.531466000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-24 23:21:41.531501000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-24 23:21:41.531510000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-24 23:21:41.531517000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-24 23:21:41.531524000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-24 23:35:55.459956000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-24 23:35:55.509207000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-24 23:35:55.689924000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-24 23:35:55.690005000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-24 23:35:55.690020000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-24 23:35:55.711409000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-24 23:35:55.711457000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-24 23:35:55.712790000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-24 23:35:55.713954000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-24 23:35:55.714227000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-24 23:35:55.714249000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-24 23:35:55.714258000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-24 23:35:55.714266000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-24 23:35:55.714274000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-24 23:36:00.421248000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "test-agent-001", "IP": "*************"}
2025-05-24 23:36:00.421279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "test-agent-001", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-24 23:36:00.422254000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:85	更新 Agent 信息	{"agentID": "test-agent-001"}
2025-05-24 23:36:00.422834000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "test-agent-001"}
2025-05-24 23:36:00.422952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "test-agent-001"}
2025-05-24 23:36:00.423639000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "test-agent-001", "taskID": "test-agent-001", "状态": "connected"}
2025-05-24 23:36:10.425922000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "test-agent-001"}
2025-05-24 23:36:44.982970000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:109	正在关闭服务...
2025-05-24 23:36:44.983169000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:93	正在停止 HTTP API 服务器
2025-05-24 23:36:44.983394000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:102	HTTP API 服务器已停止
2025-05-24 23:36:44.983424000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-24 23:36:44.983482000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "test-agent-001"}
2025-05-24 23:36:44.983494000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-24 23:36:44.983448000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-24 23:36:44.983552000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "test-agent-001", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-24 23:36:44.983907000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已关闭
2025-05-24 23:38:17.372135000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-24 23:38:17.372590000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-24 23:38:18.018405000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-24 23:38:18.018500000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-24 23:38:18.018510000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-24 23:38:18.021728000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-24 23:38:18.021764000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-24 23:38:18.023223000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-24 23:38:18.023560000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-24 23:38:18.023887000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-24 23:38:18.023913000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-24 23:38:18.023922000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-24 23:38:18.023929000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-24 23:38:18.023936000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-24 23:39:00.048832000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:109	正在关闭服务...
2025-05-24 23:39:00.048934000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:93	正在停止 HTTP API 服务器
2025-05-24 23:39:00.049194000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:102	HTTP API 服务器已停止
2025-05-24 23:39:00.049214000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-24 23:39:00.049604000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已关闭
2025-05-25 14:27:58.812622000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:27:58.931424000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:27:59.098055000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:27:59.098107000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:27:59.098117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:27:59.101453000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:27:59.101498000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:27:59.101548000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:71	初始化插件系统失败	{"error": "failed to load plugins: failed to load plugins from directory ./plugins/bin: plugin directory ./plugins/bin does not exist"}
2025-05-25 14:27:59.102660000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:27:59.103125000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 14:27:59.104058000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 14:27:59.104235000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:120	DevInsight Control Plane 已启动
2025-05-25 14:27:59.104324000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:121	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 14:27:59.104350000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:122	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 14:27:59.104372000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:123	按 Ctrl+C 退出
2025-05-25 14:30:03.501487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:30:03.515038000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:30:03.561931000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:30:03.561988000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:30:03.561998000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:30:03.565088000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:30:03.565109000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:30:03.565138000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:71	初始化插件系统失败	{"error": "failed to load plugins: failed to load plugins from directory ./plugins/bin: plugin directory ./plugins/bin does not exist"}
2025-05-25 14:30:03.565831000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:30:03.566179000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:31:21.917578000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:31:21.918089000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:31:21.944664000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:31:21.944836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:31:21.944863000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:31:21.950978000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:31:21.951035000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:31:58.954216000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:71	初始化插件系统失败	{"error": "failed to load plugins: failed to load plugins from directory ./plugins/bin: plugin directory ./plugins/bin does not exist"}
2025-05-25 14:31:59.322704000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/supported_metric_repo.go:45	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "368.045ms", "rows": 1, "sql": "SELECT * FROM `supported_metrics` WHERE metric_key = \"mysql.connections.active\" AND `supported_metrics`.`deleted_at` IS NULL ORDER BY `supported_metrics`.`id` LIMIT 1"}
2025-05-25 14:31:59.323399000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:31:59.323800000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:36:51.718927000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:36:51.743606000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:36:51.779567000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:36:51.779645000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:36:51.779658000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:36:51.785562000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:36:51.785663000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:37:00.109689000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:71	初始化插件系统失败	{"error": "failed to load plugins: failed to load plugins from directory ./plugins/bin: plugin directory ./plugins/bin does not exist"}
2025-05-25 14:37:00.115301000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:37:00.116213000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:40:07.456044000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:40:07.456689000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:40:08.174969000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:40:08.175072000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:40:08.175085000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:40:08.180280000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:40:08.180306000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:40:10.264890000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:71	初始化插件系统失败	{"error": "failed to load plugins: failed to load plugins from directory plugins/bin: plugin directory plugins/bin does not exist"}
2025-05-25 14:40:10.268717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:40:10.269586000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:96
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:44:24.723949000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:44:24.752337000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:44:25.332635000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:44:25.332751000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:44:25.332765000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:44:25.338106000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:44:25.338143000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:44:37.263719000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 14:44:37.319815000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:44:37.320196000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:47:19.727712000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:47:19.734969000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:47:19.788288000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:47:19.788342000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:47:19.788352000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:47:19.791621000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:47:19.791644000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:47:20.493670000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 14:47:20.507249000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:47:20.508089000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:50:08.399290000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:50:08.445654000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:50:08.525560000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:50:08.525603000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:50:08.525628000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:50:08.528896000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:50:08.528912000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:50:09.239447000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 14:50:09.240669000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:50:09.240932000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 14:51:59.571187000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:51:59.571908000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:51:59.692910000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:51:59.692981000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:51:59.692993000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:51:59.698046000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:51:59.698068000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:52:02.663557000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 14:52:02.665283000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:52:02.665703000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 14:52:02.666133000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 14:52:02.666148000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 14:52:02.666158000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 14:52:02.666167000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 14:52:02.666214000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 14:54:19.841676000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 14:54:19.850493000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 14:54:19.976760000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 14:54:19.976822000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 14:54:19.976832000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 14:54:19.980923000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 14:54:19.980939000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 14:54:20.727016000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 14:54:20.729397000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 14:54:20.730761000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 14:54:20.731738000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 14:54:20.731830000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 14:54:20.731846000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 14:54:20.731856000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 14:54:20.731865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 15:07:26.839868000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/plugin_handler.go:87	Failed to get plugin info	{"name": "analysis", "version": "device1", "error": "plugin analysis:device1 not registered"}
aiops/control_plane/internal/transport/http.(*PluginHandler).GetPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/plugin_handler.go:87
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
aiops/control_plane/internal/transport/http.(*Server).setupRoutes.(*Server).authMiddleware.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:259
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/recovery.go:102
github.com/gin-gonic/gin.(*Context).Next
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/context.go:185
github.com/gin-gonic/gin.(*Engine).handleHTTPRequest
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:644
github.com/gin-gonic/gin.(*Engine).ServeHTTP
	/Volumes/data/Code/Go/pkg/mod/github.com/gin-gonic/gin@v1.10.1/gin.go:600
net/http.serverHandler.ServeHTTP
	/usr/local/go/src/net/http/server.go:3301
net/http.(*conn).serve
	/usr/local/go/src/net/http/server.go:2102
2025-05-25 15:15:36.975534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 15:15:37.323325000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:96	正在停止 HTTP API 服务器
2025-05-25 15:15:37.326487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:105	HTTP API 服务器已停止
2025-05-25 15:15:37.326842000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 15:15:37.329529000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 15:15:40.656685000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 15:15:40.657238000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 15:15:40.821613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 15:15:40.821692000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 15:15:40.821701000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 15:15:40.824913000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 15:15:40.824954000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 15:15:41.631516000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 15:15:41.632687000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 15:15:41.633128000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 15:15:41.633487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 15:15:41.633500000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 15:15:41.633509000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 15:15:41.633517000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 15:15:41.633523000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 15:19:36.577920000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 15:19:36.578207000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:96	正在停止 HTTP API 服务器
2025-05-25 15:19:36.578479000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:105	HTTP API 服务器已停止
2025-05-25 15:19:36.578516000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 15:19:36.676900000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 15:19:58.188460000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 15:19:58.194916000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 15:19:58.331831000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 15:19:58.331912000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 15:19:58.331924000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 15:19:58.334484000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 15:19:58.334500000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 15:19:59.032969000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 15:19:59.034503000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 15:19:59.034823000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 15:19:59.035358000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 15:19:59.035376000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 15:19:59.035386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 15:19:59.035394000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 15:19:59.035401000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 15:40:24.506418000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 15:40:24.506901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 15:40:24.925182000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 15:40:24.925301000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 15:40:24.925314000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 15:40:24.930586000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 15:40:24.930614000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 15:40:27.911298000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 15:40:27.913082000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 15:40:27.913485000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 15:40:27.913859000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 15:40:27.913879000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 15:40:27.913891000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 15:40:27.913901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 15:40:27.913909000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 15:43:04.605435000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 15:43:04.717733000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 15:43:04.812445000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 15:43:04.812505000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 15:43:04.812514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 15:43:04.815110000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 15:43:04.815126000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 15:43:05.127453000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 15:43:05.128352000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 15:43:05.128659000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	启动 gRPC 服务器失败: %v	{"error": "failed to listen: listen tcp 0.0.0.0:50051: bind: address already in use"}
main.main
	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:43:13.872374000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 15:43:13.872546000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 15:43:13.899288000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 15:43:13.899373000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 15:43:13.899388000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 15:43:13.902282000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 15:43:13.902306000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 15:43:13.956206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 15:43:13.956613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 15:43:13.956827000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 15:43:13.957240000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 15:43:13.957252000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 15:43:13.957261000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 15:43:13.957270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 15:43:13.957277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 15:44:31.197741000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 15:44:31.588008000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:96	正在停止 HTTP API 服务器
2025-05-25 15:44:31.588257000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:105	HTTP API 服务器已停止
2025-05-25 15:44:31.588272000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 15:44:31.588356000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 15:44:49.329479000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 15:44:49.341001000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 15:44:49.524823000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 15:44:49.524938000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 15:44:49.524953000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 15:44:49.528747000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 15:44:49.528762000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 15:44:50.484206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 15:44:50.485543000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 15:44:50.488434000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 15:44:50.491502000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 15:44:50.491519000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 15:44:50.491529000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 15:44:50.491536000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 15:44:50.491557000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 15:49:55.127477000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4", "IP": "*************"}
2025-05-25 15:49:55.133154000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "72f420a6-03dd-4447-8990-8d4812ae23d4", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-25 15:49:55.134814000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "1.148ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"72f420a6-03dd-4447-8990-8d4812ae23d4\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 15:49:55.165300000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 15:49:55.167136000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 15:49:55.167371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 15:49:55.195358000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4", "taskID": "72f420a6-03dd-4447-8990-8d4812ae23d4", "状态": "connected"}
2025-05-25 15:50:10.172038000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 17:09:36.179246000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 17:09:36.179669000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 17:09:36.179671000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 17:09:36.179689000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "72f420a6-03dd-4447-8990-8d4812ae23d4", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-25 17:09:39.643179000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-25 17:09:39.645587000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 17:09:39.705239000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 17:09:39.705348000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 17:09:39.705365000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 17:09:39.709294000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 17:09:39.709340000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-25 17:09:39.711672000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 17:09:39.714601000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 17:09:39.714968000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 17:09:39.715002000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-25 17:09:39.715015000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 17:09:39.715023000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 17:09:39.715030000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-25 17:18:50.260056000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:109	正在关闭服务...
2025-05-25 17:18:50.630880000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:93	正在停止 HTTP API 服务器
2025-05-25 17:18:50.631629000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:102	HTTP API 服务器已停止
2025-05-25 17:18:50.631666000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 17:18:50.635466000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已关闭
2025-05-25 17:20:08.240466000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-25 17:20:08.281878000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 17:20:08.361236000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 17:20:08.361305000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 17:20:08.361316000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 17:20:08.366836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 17:20:08.366882000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-25 17:20:08.378749000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 17:20:08.379126000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 17:20:08.379438000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 17:20:08.379465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-25 17:20:08.379477000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 17:20:08.379504000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 17:20:08.379513000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-25 17:20:43.051839000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:109	正在关闭服务...
2025-05-25 17:20:43.053593000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:93	正在停止 HTTP API 服务器
2025-05-25 17:20:43.396568000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:102	HTTP API 服务器已停止
2025-05-25 17:20:43.396602000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 17:20:43.397778000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已关闭
2025-05-25 17:21:24.369511000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 17:21:24.374575000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 17:21:24.423109000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 17:21:24.423172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 17:21:24.423182000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 17:21:24.426544000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 17:21:24.426575000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 17:21:26.033043000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 17:21:26.034877000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 17:21:26.035507000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 17:21:26.037157000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:82	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 17:21:26.037198000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 17:21:26.037222000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 17:21:26.037242000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 17:21:26.037263000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 17:22:45.499169000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53", "IP": "*************"}
2025-05-25 17:22:45.499442000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-25 17:22:45.499674000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.173ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"06ae1344-7fff-475d-8aee-6d2cd9df1c53\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 17:22:45.501422000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:22:45.502333000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:22:45.502483000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:22:45.503325000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53", "taskID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53", "状态": "connected"}
2025-05-25 17:23:00.505272000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:29:40.484452000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:29:40.484593000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 17:29:40.484710000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "06ae1344-7fff-475d-8aee-6d2cd9df1c53", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-25 17:29:40.484627000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 19:48:14.353653000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 19:48:14.385350000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 19:48:14.463325000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 19:48:14.463411000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 19:48:14.463421000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 19:48:14.466420000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 19:48:14.466467000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 19:48:15.655795000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 19:48:15.657011000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 19:48:15.657285000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 19:49:21.252766000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 19:49:21.284651000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 19:49:21.350171000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 19:49:21.350226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 19:49:21.350235000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 19:49:21.353486000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 19:49:21.353542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 19:49:22.325035000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 19:49:22.326262000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 19:49:22.326560000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 19:50:28.692677000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 19:50:28.724238000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 19:50:28.768883000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 19:50:28.774458000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 19:50:28.774482000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 19:50:28.777239000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 19:50:28.777256000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 19:50:29.688693000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 19:50:29.690324000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 19:50:29.690703000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 19:50:29.735838000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:160	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 19:50:29.735889000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 19:50:29.735900000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 19:50:29.735908000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 19:50:29.735916000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 19:52:47.397442000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/collector_task_repo.go:91	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "153.923ms", "rows": 0, "sql": "SELECT * FROM `collector_tasks` WHERE `collector_tasks`.`deleted_at` IS NULL"}
2025-05-25 19:54:35.724129000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5", "IP": "*************"}
2025-05-25 19:54:35.774310000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-25 19:54:35.899761000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "88.752ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"c34e5940-164e-4f07-8068-e4dafd95c1a5\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 19:54:35.907055000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:54:36.040937000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:54:36.041158000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:54:36.046555000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5", "taskID": "c34e5940-164e-4f07-8068-e4dafd95c1a5", "状态": "connected"}
2025-05-25 19:54:51.137290000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:58:16.562871000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 19:58:16.594869000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:174	正在停止 HTTP API 服务器
2025-05-25 19:58:16.676675000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:183	HTTP API 服务器已停止
2025-05-25 19:58:16.677753000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 19:58:19.118979000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:260	Agent 日志数据流已关闭	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:58:19.118979000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:217	Agent 指标数据流已关闭	{"agentID": ""}
2025-05-25 19:58:19.122406000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:58:19.164014000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "c34e5940-164e-4f07-8068-e4dafd95c1a5", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-25 19:58:19.414932000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/log_repo.go:37	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "191.559ms", "rows": 4, "sql": "INSERT INTO `log_entries` (`created_at`,`updated_at`,`deleted_at`,`device_id`,`log_level`,`message`,`timestamp`,`source`,`fields`) VALUES (\"2025-05-25 19:58:19.221\",\"2025-05-25 19:58:19.221\",NULL,\"c34e5940-164e-4f07-8068-e4dafd95c1a5\",\"INFO\",\"2025-05-25 19:54:36.040937000\t\u001b[34minfo\u001b[0m\t/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122\tAgent 连接到任务流\t{\"\"agentID\"\": \"\"c34e5940-164e-4f07-8068-e4dafd95c1a5\"\"}\",1748174086,\"agent\",\"\"),(\"2025-05-25 19:58:19.221\",\"2025-05-25 19:58:19.221\",NULL,\"c34e5940-164e-4f07-8068-e4dafd95c1a5\",\"INFO\",\"2025-05-25 19:54:36.041158000\t\u001b[34minfo\u001b[0m\t/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160\tAgent 流注册成功\t{\"\"agentID\"\": \"\"c34e5940-164e-4f07-8068-e4dafd95c1a5\"\"}\",1748174086,\"agent\",\"\"),(\"2025-05-25 19:58:19.221\",\"2025-05-25 19:58:19.221\",NULL,\"c34e5940-164e-4f07-8068-e4dafd95c1a5\",\"INFO\",\"2025-05-25 19:54:36.046555000\t\u001b[34minfo\u001b[0m\t/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207\t更新任务状态\t{\"\"agentID\"\": \"\"c34e5940-164e-4f07-8068-e4dafd95c1a5\"\", \"\"taskID\"\": \"\"c34e5940-164e-4f07-8068-e4dafd95c1a5\"\", \"\"状态\"\": \"\"connected\"\"}\",1748174086,\"agent\",\"\"),(\"2025-05-25 19:58:19.221\",\"2025-05-25 19:58:19.221\",NULL,\"c34e5940-164e-4f07-8068-e4dafd95c1a5\",\"INFO\",\"2025-05-25 19:54:51.137290000\t\u001b[34minfo\u001b[0m\t/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271\tAgent 连接到日志数据流\t{\"\"agentID\"\": \"\"c34e5940-164e-4f07-8068-e4dafd95c1a5\"\"}\",1748174096,\"agent\",\"\") RETURNING `id`"}
2025-05-25 19:58:19.415965000	[35mdebug[0m	LogService	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/log_service.go:47	成功保存日志数据	{"count": 4}
2025-05-25 19:58:19.416065000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 20:02:33.169766000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 20:02:33.208232000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 20:02:33.313874000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 20:02:33.313966000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 20:02:33.313977000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 20:02:33.319253000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 20:02:33.319305000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 20:02:34.282166000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 20:02:34.283267000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 20:02:34.322951000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 20:02:34.356129000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:160	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 20:02:34.356178000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 20:02:34.356209000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 20:02:34.356220000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 20:02:34.356228000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 20:09:51.915371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 20:09:52.067373000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:174	正在停止 HTTP API 服务器
2025-05-25 20:09:52.106648000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:183	HTTP API 服务器已停止
2025-05-25 20:09:52.106724000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 20:09:52.198087000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 20:10:30.346174000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 20:10:30.351967000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 20:10:30.434147000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 20:10:30.434206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 20:10:30.434216000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 20:10:30.437035000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 20:10:30.437079000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 20:10:31.397104000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 20:10:31.398422000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 20:10:31.398689000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 20:10:31.451941000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:160	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 20:10:31.451995000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 20:10:31.452005000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 20:10:31.452016000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 20:10:31.452024000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 20:13:12.884866000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 20:13:13.235735000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:174	正在停止 HTTP API 服务器
2025-05-25 20:13:13.235956000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:183	HTTP API 服务器已停止
2025-05-25 20:13:13.235972000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 20:13:13.236075000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 20:13:24.521078000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 20:13:24.521905000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 20:13:24.638547000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 20:13:24.638637000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 20:13:24.638655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 20:13:24.641866000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 20:13:24.641895000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 20:13:25.544423000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 20:13:25.545993000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 20:13:25.547112000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 20:13:25.583034000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:160	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 20:13:25.583090000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 20:13:25.583123000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 20:13:25.583134000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 20:13:25.583144000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 20:15:59.677680000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	正在关闭服务...
2025-05-25 20:15:59.680996000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:174	正在停止 HTTP API 服务器
2025-05-25 20:15:59.684862000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:183	HTTP API 服务器已停止
2025-05-25 20:15:59.684953000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-25 20:15:59.781101000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:158	DevInsight Control Plane 已关闭
2025-05-25 20:20:47.667211000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 20:20:47.947862000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 20:20:48.386385000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 20:20:48.386481000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 20:20:48.386494000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 20:20:48.391576000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 20:20:48.391594000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 20:20:49.077018000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 20:20:49.078818000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 20:20:49.079226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 20:20:49.132857000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:160	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 20:20:49.133036000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 20:20:49.133069000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 20:20:49.133081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 20:20:49.133090000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 20:20:58.400031000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f", "IP": "*************"}
2025-05-25 20:20:58.400711000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-25 20:20:58.435779000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "34.978ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"e5f30ce7-8f0b-46bb-90ac-d23d6073926f\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 20:20:58.683823000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:32	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "247.889ms", "rows": 1, "sql": "INSERT INTO `agents` (`created_at`,`updated_at`,`deleted_at`,`agent_id`,`agent_ip`,`supported_collector_types`,`status`,`last_heartbeat`,`device_config`) VALUES (\"2025-05-25 20:20:58.435\",\"2025-05-25 20:20:58.435\",NULL,\"e5f30ce7-8f0b-46bb-90ac-d23d6073926f\",\"*************\",\"[system mysql redis docker]\",\"online\",\"2025-05-25 20:20:58.435\",\"\") RETURNING `id`"}
2025-05-25 20:20:58.683883000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f"}
2025-05-25 20:20:58.685342000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f"}
2025-05-25 20:20:58.685692000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f"}
2025-05-25 20:20:58.930199000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/collector_task_repo.go:101	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "244.449ms", "rows": 0, "sql": "SELECT * FROM `collector_tasks` WHERE agent_id = \"e5f30ce7-8f0b-46bb-90ac-d23d6073926f\" AND `collector_tasks`.`deleted_at` IS NULL"}
2025-05-25 20:20:58.934882000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f", "taskID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f", "状态": "connected"}
2025-05-25 20:21:13.688644000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f"}
2025-05-25 20:26:56.339553000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-25 20:26:56.444036000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-25 20:26:56.706779000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-25 20:26:56.706857000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-25 20:26:56.706870000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-25 20:26:56.712011000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-25 20:26:56.712033000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-25 20:26:57.443659000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-25 20:26:57.445768000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-25 20:26:57.446188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-25 20:26:57.496197000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:160	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-25 20:26:57.496226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-25 20:26:57.496255000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-25 20:26:57.496270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-25 20:26:57.496279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-25 20:27:12.260655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa", "IP": "*************"}
2025-05-25 20:27:12.261271000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "d0a9d1e7-3045-432f-9d06-636124456daa", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-25 20:27:12.261544000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.189ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"d0a9d1e7-3045-432f-9d06-636124456daa\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 20:27:12.261809000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:27:12.263262000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:27:12.263638000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:27:12.430362000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/collector_task_repo.go:101	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "166.631ms", "rows": 0, "sql": "SELECT * FROM `collector_tasks` WHERE agent_id = \"d0a9d1e7-3045-432f-9d06-636124456daa\" AND `collector_tasks`.`deleted_at` IS NULL"}
2025-05-25 20:27:12.431434000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa", "taskID": "d0a9d1e7-3045-432f-9d06-636124456daa", "状态": "connected"}
2025-05-25 20:27:27.268988000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:35:04.060845000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:73	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "140.534ms", "rows": 14, "sql": "SELECT * FROM `agents` WHERE `agents`.`deleted_at` IS NULL"}
2025-05-25 20:38:43.975329000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/alert_repo.go:158	trace	{"slow": "SLOW SQL >= 100ms", "elapsed": "117.359ms", "rows": 0, "sql": "SELECT * FROM `alert_events` WHERE status = \"firing\" AND `alert_events`.`deleted_at` IS NULL"}
2025-05-25 20:44:26.716977000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:44:26.691135000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "d0a9d1e7-3045-432f-9d06-636124456daa", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-25 20:44:26.691141000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-25 20:44:26.691109000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 09:12:13.953048000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 09:12:13.980689000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 09:12:14.046239000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 09:12:14.046300000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 09:12:14.046312000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 09:12:14.053355000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 09:12:14.053435000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 09:12:16.621642000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 09:12:16.623618000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 09:12:16.624196000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 09:18:45.380561000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 09:18:45.386455000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 09:18:45.463196000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 09:18:45.463278000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 09:18:45.463292000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 09:18:45.469511000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 09:18:45.469584000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 09:18:47.384761000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 09:18:47.476735000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 09:18:47.477339000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 09:19:05.288354000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 09:19:05.288783000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 09:19:05.517720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 09:19:05.517786000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 09:19:05.517806000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 09:19:05.524273000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 09:19:05.524339000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 09:19:06.327237000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 09:19:06.328849000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 09:19:06.329251000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 09:19:06.329605000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 09:19:06.329626000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-26 09:19:06.329636000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 09:19:06.329650000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 09:19:06.329657000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-26 09:19:34.927319000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063", "IP": "*************"}
2025-05-26 09:19:34.948952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-26 09:19:34.949349000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.249ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"b421ec9c-5a3b-4b52-8193-0e42ba58f063\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 09:19:34.949655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 09:19:34.951319000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 09:19:34.951840000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 09:19:34.962979000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063", "taskID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063", "状态": "connected"}
2025-05-26 09:19:49.953295000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 10:25:53.341023000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 10:25:53.333754000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 10:25:53.298376000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 10:25:53.298376000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "b421ec9c-5a3b-4b52-8193-0e42ba58f063", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-26 10:51:09.532105000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 10:51:09.555112000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 10:51:09.570016000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 10:51:09.570103000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 10:51:09.570117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 10:51:09.575865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 10:51:09.575918000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 10:51:10.254120000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 10:51:10.256413000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 10:51:10.256819000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 10:51:10.257163000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 10:51:10.257182000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-26 10:51:10.257193000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 10:51:10.257202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 10:51:10.257209000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-26 10:56:39.810978000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37", "IP": "*************"}
2025-05-26 10:56:39.820201000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-26 10:56:39.820660000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.277ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"bdaf92ef-438d-4798-9aa9-4eb62e979c37\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:97
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:164
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 10:56:39.856894000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:56:39.858331000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:56:39.858595000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:56:39.859430000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37", "taskID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37", "状态": "connected"}
2025-05-26 10:56:54.860402000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:57:02.828174000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 10:57:02.828183000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 10:57:02.828236000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:57:02.828277000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "bdaf92ef-438d-4798-9aa9-4eb62e979c37", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-26 11:39:58.957831000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 11:39:58.985613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 11:39:59.089253000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 11:39:59.089365000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 11:39:59.089386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 11:39:59.095588000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 11:39:59.095745000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 11:40:00.672471000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 11:40:00.674687000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 11:40:00.675330000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 11:40:00.676037000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 11:40:00.676080000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-26 11:40:00.676180000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 11:40:00.676285000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 11:40:00.676308000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-26 11:43:48.691218000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 11:43:48.705579000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 11:43:48.759631000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 11:43:48.759779000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 11:43:48.759796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 11:43:48.771092000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 11:43:48.771134000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 11:43:51.311871000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 11:43:51.314191000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 11:43:51.315456000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 11:43:51.316571000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 11:43:51.316618000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:138	DevInsight Control Plane 已启动
2025-05-26 11:43:51.316655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:139	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 11:43:51.316714000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:140	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 11:43:51.316729000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:141	按 Ctrl+C 退出
2025-05-26 14:02:05.161279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:26	DevInsight Control Plane 正在启动...
2025-05-26 14:02:05.182987000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 14:02:05.249180000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 14:02:05.249288000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 14:02:05.249303000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 14:02:05.256613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 14:02:05.256676000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:37	数据库初始化成功
2025-05-26 14:02:06.126631000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:88	插件系统初始化成功
2025-05-26 14:02:06.128725000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 14:02:06.129519000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 14:02:06.130080000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 14:02:06.130160000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:149	DevInsight Control Plane 已启动
2025-05-26 14:02:06.130174000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:150	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 14:02:06.130184000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:151	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 14:02:06.130217000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:152	按 Ctrl+C 退出
2025-05-26 14:18:51.979172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 14:18:52.021932000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 14:18:52.142989000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 14:18:52.143080000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 14:18:52.143093000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 14:18:52.146754000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 14:18:52.146774000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 14:18:52.146784000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 14:18:52.147943000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 14:18:52.149416000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 14:18:52.149708000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 14:18:52.149719000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 14:18:52.149727000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 14:18:52.149735000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 14:18:52.149743000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 14:18:53.885898000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:125	正在关闭服务...
2025-05-26 14:18:53.886121000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	正在停止 HTTP API 服务器
2025-05-26 14:18:53.886646000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:106	HTTP API 服务器已停止
2025-05-26 14:18:53.886838000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:92	正在停止 gRPC 服务器
2025-05-26 14:18:53.887150000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:134	DevInsight Control Plane 已关闭
2025-05-26 14:19:38.438669000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 14:19:38.482155000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 14:19:38.536593000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 14:19:38.536701000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 14:19:38.536717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 14:19:38.542818000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 14:19:38.542876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 14:19:38.542988000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 14:19:38.544598000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 14:19:38.544985000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 14:19:38.545338000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 14:19:38.545375000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 14:19:38.545386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 14:19:38.545395000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 14:19:38.545402000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 14:21:43.132039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:99	接收到 Agent 注册请求	{"agentID": "559575c1-863a-4566-8669-4be872f3697e", "IP": "*************"}
2025-05-26 14:21:43.159182000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "559575c1-863a-4566-8669-4be872f3697e", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-26 14:21:43.201619000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "41.051ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"559575c1-863a-4566-8669-4be872f3697e\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:100
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:228
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:21:43.237842000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "559575c1-863a-4566-8669-4be872f3697e"}
2025-05-26 14:21:43.288717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:125	Agent 连接到任务流	{"agentID": "559575c1-863a-4566-8669-4be872f3697e"}
2025-05-26 14:21:43.288731000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:138	建立插件更新推送流连接
2025-05-26 14:21:43.288974000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "559575c1-863a-4566-8669-4be872f3697e"}
2025-05-26 14:21:43.306881000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "559575c1-863a-4566-8669-4be872f3697e", "taskID": "559575c1-863a-4566-8669-4be872f3697e", "状态": "connected"}
2025-05-26 14:21:58.276322000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:274	Agent 连接到日志数据流	{"agentID": "559575c1-863a-4566-8669-4be872f3697e"}
2025-05-26 14:28:23.351578000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:195	stream context 已完成	{"agentID": "559575c1-863a-4566-8669-4be872f3697e"}
2025-05-26 14:28:23.253046000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:145	插件更新推送流连接断开
2025-05-26 14:28:23.325991000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:255
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:28:23.326037000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158	接收任务状态失败	{"agentID": "559575c1-863a-4566-8669-4be872f3697e", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158
2025-05-26 14:28:23.326049000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:248
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:49:00.092887000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 14:49:00.143230000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 14:49:00.199651000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 14:49:00.199713000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 14:49:00.199724000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 14:49:00.204859000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 14:49:00.204879000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 14:49:00.204994000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 14:49:00.206492000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 14:49:00.206869000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 14:49:00.207169000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 14:49:00.207182000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 14:49:00.207191000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 14:49:00.207198000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 14:49:00.207205000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 14:49:13.350464000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:99	接收到 Agent 注册请求	{"agentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "IP": "*************"}
2025-05-26 14:49:13.351267000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-26 14:49:13.373364000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "21.291ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"0088ab7b-0ecc-45fe-b1b4-9373401a3663\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:100
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:228
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:49:13.400864000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663"}
2025-05-26 14:49:13.415729000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "plugin_name": "system-collector", "plugin_version": "", "device_type": "system", "architecture": "arm64", "os": "darwin"}
2025-05-26 14:49:13.415848000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: system-collector (device_type: system, arch: arm64, os: darwin)", "plugin_name": "system-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:49:13.432408000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "plugin_name": "mysql-collector", "plugin_version": "", "device_type": "mysql", "architecture": "arm64", "os": "darwin"}
2025-05-26 14:49:13.432532000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: mysql-collector (device_type: mysql, arch: arm64, os: darwin)", "plugin_name": "mysql-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:49:13.433163000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "plugin_name": "redis-collector", "plugin_version": "", "device_type": "redis", "architecture": "arm64", "os": "darwin"}
2025-05-26 14:49:13.433224000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: redis-collector (device_type: redis, arch: arm64, os: darwin)", "plugin_name": "redis-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:49:13.433869000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "plugin_name": "docker-collector", "plugin_version": "", "device_type": "docker", "architecture": "arm64", "os": "darwin"}
2025-05-26 14:49:13.433898000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: docker-collector (device_type: docker, arch: arm64, os: darwin)", "plugin_name": "docker-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 14:49:13.436857000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:138	建立插件更新推送流连接
2025-05-26 14:49:13.437315000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:125	Agent 连接到任务流	{"agentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663"}
2025-05-26 14:49:13.437622000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663"}
2025-05-26 14:49:13.473467000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "taskID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663", "状态": "connected"}
2025-05-26 14:49:28.459203000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:274	Agent 连接到日志数据流	{"agentID": "0088ab7b-0ecc-45fe-b1b4-9373401a3663"}
2025-05-26 15:05:29.041216000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 15:05:29.066554000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 15:05:29.117463000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 15:05:29.117607000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 15:05:29.117624000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 15:05:29.124422000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 15:05:29.124498000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 15:05:29.124627000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 15:05:29.124661000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:657	开始自动发现插件	{"plugin_dir": "./plugins/storage"}
2025-05-26 15:05:29.125150000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/storage"}
2025-05-26 15:05:29.125272000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/build"}
2025-05-26 15:05:29.135201000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:751	无法推断插件设备类型	{"plugin_name": "email-alerter"}
2025-05-26 15:05:29.135272000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:751	无法推断插件设备类型	{"plugin_name": "enhanced-analyzer"}
2025-05-26 15:05:29.311377000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "mysql-collector-1.0.0", "name": "mysql-collector", "version": "1.0.0", "device_types": ["mysql"], "path": "plugins/build/mysql-collector.so"}
2025-05-26 15:05:29.311496000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:751	无法推断插件设备类型	{"plugin_name": "simple-analyzer"}
2025-05-26 15:05:29.437655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "system-collector-1.0.0", "name": "system-collector", "version": "1.0.0", "device_types": ["system"], "path": "plugins/build/system-collector.so"}
2025-05-26 15:05:29.437733000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/system-collector"}
2025-05-26 15:05:29.437829000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/mysql-collector"}
2025-05-26 15:05:29.437875000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:683	插件自动发现完成	{"total_registered": 5, "total_plugins": 2}
2025-05-26 15:05:29.439444000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 15:05:29.439824000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 15:05:29.440156000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 15:05:29.440171000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 15:05:29.440196000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 15:05:29.440206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 15:05:29.440214000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 15:06:37.170606000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:99	接收到 Agent 注册请求	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496", "IP": "*************"}
2025-05-26 15:06:37.171310000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "4b083da0-ac23-4869-b265-870c3bd4e496", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-26 15:06:37.188838000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "17.445ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"4b083da0-ac23-4869-b265-870c3bd4e496\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:100
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:228
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:06:37.218326000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496"}
2025-05-26 15:06:37.244613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "4b083da0-ac23-4869-b265-870c3bd4e496", "plugin_name": "system-collector", "plugin_version": "", "device_type": "system", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:06:37.244788000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81	加载插件二进制文件失败	{"error": "插件文件不存在: plugins/storage/plugins/build/system-collector.so", "binary_path": "plugins/build/system-collector.so"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:06:37.260858000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "4b083da0-ac23-4869-b265-870c3bd4e496", "plugin_name": "mysql-collector", "plugin_version": "", "device_type": "mysql", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:06:37.261045000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81	加载插件二进制文件失败	{"error": "插件文件不存在: plugins/storage/plugins/build/mysql-collector.so", "binary_path": "plugins/build/mysql-collector.so"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:06:37.261583000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "4b083da0-ac23-4869-b265-870c3bd4e496", "plugin_name": "redis-collector", "plugin_version": "", "device_type": "redis", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:06:37.261612000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: redis-collector (device_type: redis, arch: arm64, os: darwin)", "plugin_name": "redis-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:06:37.262134000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "4b083da0-ac23-4869-b265-870c3bd4e496", "plugin_name": "docker-collector", "plugin_version": "", "device_type": "docker", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:06:37.262176000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: docker-collector (device_type: docker, arch: arm64, os: darwin)", "plugin_name": "docker-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:06:37.264525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:138	建立插件更新推送流连接
2025-05-26 15:06:37.264883000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:125	Agent 连接到任务流	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496"}
2025-05-26 15:06:37.265225000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496"}
2025-05-26 15:06:37.285432000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496", "taskID": "4b083da0-ac23-4869-b265-870c3bd4e496", "状态": "connected"}
2025-05-26 15:06:52.277475000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:274	Agent 连接到日志数据流	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496"}
2025-05-26 15:08:07.222105000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158	接收任务状态失败	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158
2025-05-26 15:08:07.222120000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:255
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:08:07.222103000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:145	插件更新推送流连接断开
2025-05-26 15:08:07.222107000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:248
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:08:07.224130000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:195	stream context 已完成	{"agentID": "4b083da0-ac23-4869-b265-870c3bd4e496"}
2025-05-26 15:26:38.486744000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 15:26:38.513554000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 15:26:38.581206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 15:26:38.581292000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 15:26:38.581305000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 15:26:38.587879000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 15:26:38.587942000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 15:26:38.588050000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 15:26:38.588098000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:657	开始自动发现插件	{"plugin_dir": "./plugins/storage"}
2025-05-26 15:26:38.588156000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/storage"}
2025-05-26 15:26:38.588260000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/build"}
2025-05-26 15:26:38.710470000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "email-alerter-1.0.0", "name": "email-alerter", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/email-alerter.so"}
2025-05-26 15:26:38.840667000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "enhanced-analyzer-1.0.0", "name": "enhanced-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/enhanced-analyzer.so"}
2025-05-26 15:26:38.966497000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "mysql-collector-1.0.0", "name": "mysql-collector", "version": "1.0.0", "device_types": ["mysql"], "path": "plugins/build/mysql-collector.so"}
2025-05-26 15:26:39.093424000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "simple-analyzer-1.0.0", "name": "simple-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/simple-analyzer.so"}
2025-05-26 15:26:39.213717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "system-collector-1.0.0", "name": "system-collector", "version": "1.0.0", "device_types": ["system"], "path": "plugins/build/system-collector.so"}
2025-05-26 15:26:39.213784000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/system-collector"}
2025-05-26 15:26:39.213892000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/mysql-collector"}
2025-05-26 15:26:39.213939000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:683	插件自动发现完成	{"total_registered": 5, "total_plugins": 5}
2025-05-26 15:26:39.215530000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 15:26:39.215942000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 15:26:39.216525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 15:26:39.216563000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 15:26:39.216576000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 15:26:39.216602000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 15:26:39.216653000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 15:27:22.273545000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:99	接收到 Agent 注册请求	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3", "IP": "*************"}
2025-05-26 15:27:22.535559000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "28738140-0f0d-43d7-af08-84d96dabb9f3", "IP": "*************", "支持的采集器类型": ["system", "mysql", "email", "simple"]}
2025-05-26 15:27:22.536018000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.336ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"28738140-0f0d-43d7-af08-84d96dabb9f3\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:100
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:228
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:27:22.536465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3"}
2025-05-26 15:27:22.537227000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "28738140-0f0d-43d7-af08-84d96dabb9f3", "plugin_name": "system-collector", "plugin_version": "", "device_type": "system", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:27:22.537409000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81	加载插件二进制文件失败	{"error": "插件文件不存在: plugins/storage/plugins/build/system-collector.so", "binary_path": "plugins/build/system-collector.so"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:27:22.537938000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "28738140-0f0d-43d7-af08-84d96dabb9f3", "plugin_name": "mysql-collector", "plugin_version": "", "device_type": "mysql", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:27:22.538013000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81	加载插件二进制文件失败	{"error": "插件文件不存在: plugins/storage/plugins/build/mysql-collector.so", "binary_path": "plugins/build/mysql-collector.so"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:81
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:27:22.538413000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "28738140-0f0d-43d7-af08-84d96dabb9f3", "plugin_name": "email-collector", "plugin_version": "", "device_type": "email", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:27:22.538434000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: email-collector (device_type: email, arch: arm64, os: darwin)", "plugin_name": "email-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:27:22.538704000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "28738140-0f0d-43d7-af08-84d96dabb9f3", "plugin_name": "simple-collector", "plugin_version": "", "device_type": "simple", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:27:22.538845000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69	获取插件信息失败	{"error": "未找到兼容的插件: simple-collector (device_type: simple, arch: arm64, os: darwin)", "plugin_name": "simple-collector"}
aiops/control_plane/internal/service.(*PluginDistributionService).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:69
aiops/control_plane/internal/transport/grpc.(*Server).RequestPlugin
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:318
aiops/pkg/proto._AgentService_RequestPlugin_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:267
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:27:22.540030000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:138	建立插件更新推送流连接
2025-05-26 15:27:22.540091000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:125	Agent 连接到任务流	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3"}
2025-05-26 15:27:22.540491000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3"}
2025-05-26 15:27:22.551123000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3", "taskID": "28738140-0f0d-43d7-af08-84d96dabb9f3", "状态": "connected"}
2025-05-26 15:27:37.542598000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:274	Agent 连接到日志数据流	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3"}
2025-05-26 15:34:48.392847000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:145	插件更新推送流连接断开
2025-05-26 15:34:48.392879000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158	接收任务状态失败	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158
2025-05-26 15:34:48.392962000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:195	stream context 已完成	{"agentID": "28738140-0f0d-43d7-af08-84d96dabb9f3"}
2025-05-26 15:34:48.392941000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:255
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:34:48.393010000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:248
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:38:03.346499000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 15:38:03.357069000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 15:38:04.725368000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 15:38:04.725513000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 15:38:04.725528000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 15:38:04.731497000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 15:38:04.731542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 15:38:04.731644000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 15:38:04.731676000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:657	开始自动发现插件	{"plugin_dir": "./plugins/storage"}
2025-05-26 15:38:04.731738000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/storage"}
2025-05-26 15:38:04.731881000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/build"}
2025-05-26 15:38:04.880573000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "email-alerter-1.0.0", "name": "email-alerter", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/email-alerter.so"}
2025-05-26 15:38:05.009008000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "enhanced-analyzer-1.0.0", "name": "enhanced-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/enhanced-analyzer.so"}
2025-05-26 15:38:05.134873000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "mysql-collector-1.0.0", "name": "mysql-collector", "version": "1.0.0", "device_types": ["mysql"], "path": "plugins/build/mysql-collector.so"}
2025-05-26 15:38:05.261784000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "simple-analyzer-1.0.0", "name": "simple-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/simple-analyzer.so"}
2025-05-26 15:38:05.382046000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "system-collector-1.0.0", "name": "system-collector", "version": "1.0.0", "device_types": ["system"], "path": "plugins/build/system-collector.so"}
2025-05-26 15:38:05.382110000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/system-collector"}
2025-05-26 15:38:05.382219000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/mysql-collector"}
2025-05-26 15:38:05.382266000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:683	插件自动发现完成	{"total_registered": 5, "total_plugins": 5}
2025-05-26 15:38:05.383918000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 15:38:05.384301000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:79	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 15:38:05.384781000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 15:38:05.384816000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 15:38:05.384850000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 15:38:05.384861000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 15:38:05.384884000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 15:38:19.702829000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:99	接收到 Agent 注册请求	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416", "IP": "*************"}
2025-05-26 15:38:19.708341000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "158b853c-b9ea-482f-a21d-c0a50a269416", "IP": "*************", "支持的采集器类型": ["system", "mysql"]}
2025-05-26 15:38:19.708867000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.400ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"158b853c-b9ea-482f-a21d-c0a50a269416\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:100
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:228
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:38:19.709263000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416"}
2025-05-26 15:38:19.710059000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:51	收到插件请求	{"agent_id": "158b853c-b9ea-482f-a21d-c0a50a269416", "plugin_name": "system-collector", "plugin_version": "", "device_type": "system", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:38:19.838734000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:370	记录插件分发事件	{"agent_id": "158b853c-b9ea-482f-a21d-c0a50a269416", "plugin_name": "system-collector", "version": "1.0.0", "status": "success"}
2025-05-26 15:38:19.840946000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:51	收到插件请求	{"agent_id": "158b853c-b9ea-482f-a21d-c0a50a269416", "plugin_name": "mysql-collector", "plugin_version": "", "device_type": "mysql", "architecture": "arm64", "os": "darwin"}
2025-05-26 15:38:19.976024000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:370	记录插件分发事件	{"agent_id": "158b853c-b9ea-482f-a21d-c0a50a269416", "plugin_name": "mysql-collector", "version": "1.0.0", "status": "success"}
2025-05-26 15:38:19.978972000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:139	建立插件更新推送流连接
2025-05-26 15:38:19.978983000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:125	Agent 连接到任务流	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416"}
2025-05-26 15:38:19.979393000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416"}
2025-05-26 15:38:19.998742000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416", "taskID": "158b853c-b9ea-482f-a21d-c0a50a269416", "状态": "connected"}
2025-05-26 15:38:34.981219000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:274	Agent 连接到日志数据流	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416"}
2025-05-26 15:42:18.422649000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:146	插件更新推送流连接断开
2025-05-26 15:42:18.422591000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:224
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:248
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:42:18.422591000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158	接收任务状态失败	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:158
2025-05-26 15:42:18.422674000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:267
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:255
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 15:42:18.422630000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:195	stream context 已完成	{"agentID": "158b853c-b9ea-482f-a21d-c0a50a269416"}
2025-05-26 16:01:15.125980000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 16:01:15.127265000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 16:01:15.207885000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 16:01:15.208019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 16:01:15.208033000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 16:01:15.214459000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 16:01:15.214529000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 16:01:15.214688000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 16:01:15.214723000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:657	开始自动发现插件	{"plugin_dir": "./plugins/storage"}
2025-05-26 16:01:15.214781000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/storage"}
2025-05-26 16:01:15.229262000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/build"}
2025-05-26 16:01:15.371970000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "email-alerter-1.0.0", "name": "email-alerter", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/email-alerter.so"}
2025-05-26 16:01:15.502306000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "enhanced-analyzer-1.0.0", "name": "enhanced-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/enhanced-analyzer.so"}
2025-05-26 16:01:15.628119000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "mysql-collector-1.0.0", "name": "mysql-collector", "version": "1.0.0", "device_types": ["mysql"], "path": "plugins/build/mysql-collector.so"}
2025-05-26 16:01:15.755043000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "simple-analyzer-1.0.0", "name": "simple-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/simple-analyzer.so"}
2025-05-26 16:01:15.875276000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "system-collector-1.0.0", "name": "system-collector", "version": "1.0.0", "device_types": ["system"], "path": "plugins/build/system-collector.so"}
2025-05-26 16:01:15.875375000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/system-collector"}
2025-05-26 16:01:15.875539000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/mysql-collector"}
2025-05-26 16:01:15.875598000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:683	插件自动发现完成	{"total_registered": 5, "total_plugins": 5}
2025-05-26 16:01:15.877602000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 16:01:15.878117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:83	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 16:01:15.878473000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 16:01:15.878493000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 16:01:15.878505000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 16:01:15.878514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 16:01:15.878522000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 16:05:14.516381000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025-05-26 16:05:14.517321000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 16:05:14.622543000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 16:05:14.622716000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 16:05:14.622740000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 16:05:14.628082000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 16:05:14.628120000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 16:05:14.628239000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 16:05:14.628282000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:657	开始自动发现插件	{"plugin_dir": "./plugins/storage"}
2025-05-26 16:05:14.628381000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/storage"}
2025-05-26 16:05:14.628506000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/build"}
2025-05-26 16:05:14.785881000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "email-alerter-1.0.0", "name": "email-alerter", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/email-alerter.so"}
2025-05-26 16:05:14.914434000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "enhanced-analyzer-1.0.0", "name": "enhanced-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/enhanced-analyzer.so"}
2025-05-26 16:05:15.073244000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "mysql-collector-1.0.0", "name": "mysql-collector", "version": "1.0.0", "device_types": ["mysql"], "path": "plugins/build/mysql-collector.so"}
2025-05-26 16:05:15.200116000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "simple-analyzer-1.0.0", "name": "simple-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/simple-analyzer.so"}
2025-05-26 16:05:15.320453000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:795	自动注册插件成功	{"plugin_id": "system-collector-1.0.0", "name": "system-collector", "version": "1.0.0", "device_types": ["system"], "path": "plugins/build/system-collector.so"}
2025-05-26 16:05:15.320527000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/system-collector"}
2025-05-26 16:05:15.320657000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:697	扫描插件目录	{"dir": "./plugins/examples/mysql-collector"}
2025-05-26 16:05:15.320720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:683	插件自动发现完成	{"total_registered": 5, "total_plugins": 5}
2025-05-26 16:05:15.323090000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 16:05:15.323518000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:83	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
2025-05-26 16:05:15.323893000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 16:05:15.323910000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 16:05:15.323921000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 16:05:15.323930000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 16:05:15.323948000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
